<template>
  <view class="course-series-list">
    <view v-if="loading" class="loading-container">
      <text>加载中...</text>
    </view>

    <view v-else class="series-content">
      <view
        v-for="item in courseSeriesList"
        :key="item.seriesId"
        class="course-series-item"
        @click="handleCourseSeriesClick(item)"
      >
        <!-- 课程信息卡片 -->
        <view class="course-info-card">
          <image
            class="course-cover"
            :src="
              item.defaultCourse?.coverUrl ||
              '/static/images/default-course.png'
            "
            mode="aspectFill"
          />
          <view class="course-info">
            <text class="course-name">{{ item.seriesName }}</text>
            <text class="course-progress">{{ formatProgress(item) }}</text>
            <text class="course-count"
              >{{ item.allCourses?.length || 0 }}册课程</text
            >
          </view>
          <view class="action-arrow">
            <text class="arrow-icon">›</text>
          </view>
        </view>

        <!-- 今日待学课节 (如果有的话) -->
        <view v-if="item.todayLesson" class="today-lesson-card">
          <view class="today-label">今日待学</view>
          <view class="lesson-item">
            <image
              class="lesson-icon"
              :src="
                item.todayLesson.lessonCoverUrl ||
                '/static/images/default-lesson.png'
              "
              mode="aspectFill"
            />
            <view class="lesson-info">
              <text class="lesson-name">{{ item.todayLesson.lessonName }}</text>
              <text class="lesson-desc">{{
                item.todayLesson.lessonDescription
              }}</text>
            </view>
            <view class="lesson-status">
              <text class="status-icon">🔒</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import {
  getCourseSeriesDetail,
  getCourseSeriesExercise,
  getMyCourseCategoryList,
  type CourseCategoryItem,
  type CourseSeriesDetail,
  type CourseSeriesExercise as ExerciseData,
  type LessonDetailInfo,
} from "@/api/homework";
import { onMounted, ref } from "vue";

interface CourseSeriesItem extends CourseSeriesDetail {
  progressStats?: {
    totalLessons: number;
    completedLessons: number;
    completionPercentage: number;
    consecutiveDays: number;
    weeklyStudyDays: number;
  };
  todayLesson?: LessonDetailInfo;
}

interface Props {
  // 移除原来的 props，改为内部获取数据
}

interface Emits {
  (e: "course-series-click", item: CourseSeriesItem): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 使用 ref 替代 reactive，更符合 Vue3 最佳实践
const courseSeriesList = ref<CourseSeriesItem[]>([]);
const loading = ref(true);

// 获取课程系列列表数据
const fetchCourseSeriesList = async () => {
  try {
    loading.value = true;
    console.log("开始获取课程系列列表数据");

    // 首先获取分类列表
    const categoryRes = await getMyCourseCategoryList();
    console.log("course category res", categoryRes);
    console.log("course category raw data", categoryRes.data);

    const categories: CourseCategoryItem[] =
      categoryRes.data || categoryRes.getData();
    console.log("解析后的分类数据:", categories);

    if (categories && Array.isArray(categories)) {
      // 过滤出系列类型的分类
      const seriesCategories = categories.filter(
        (item) => item.type === "series"
      );
      console.log("过滤出的系列分类:", seriesCategories);

      if (seriesCategories.length === 0) {
        console.log("没有找到系列类型的分类，直接设置空数组");
        courseSeriesList.value = [];
        loading.value = false;
        return;
      }

      // 为每个系列获取详细信息
      const seriesPromises = seriesCategories.map(async (category) => {
        console.log(`处理系列分类 ${category.id}: ${category.name}`);

        const seriesItem: CourseSeriesItem = {
          seriesId: category.id,
          seriesName: category.seriesName || category.name,
          defaultCourse: {
            id: category.id,
            name: category.seriesName || category.name,
            status: 1,
            createTime: new Date().toISOString(),
          },
          allCourses: [
            {
              id: category.id,
              name: category.seriesName || category.name,
              status: 1,
              createTime: new Date().toISOString(),
            },
          ],
        };

        // 尝试获取系列详情
        try {
          const detailRes = await getCourseSeriesDetail(category.id);
          console.log(`系列 ${category.id} 详情响应:`, detailRes);

          const detailData = detailRes.data || detailRes.getData();
          console.log(`系列 ${category.id} 详情数据:`, detailData);

          if (detailData) {
            seriesItem.seriesName = detailData.seriesName;
            seriesItem.defaultCourse = detailData.defaultCourse;
            seriesItem.allCourses = detailData.allCourses;
          }
        } catch (error) {
          console.warn(`获取系列 ${category.id} 详情失败:`, error);
        }

        // 尝试获取练习数据来补充进度信息
        try {
          const exerciseRes = await getCourseSeriesExercise(category.id);
          const exerciseData: ExerciseData =
            exerciseRes.data || exerciseRes.getData();
          if (exerciseData) {
            seriesItem.progressStats = exerciseData.progressStats;
            // 设置今日待学课节
            if (exerciseData.lessons && exerciseData.lessons.length > 0) {
              const firstUncompletedLesson = exerciseData.lessons.find(
                (lesson: any) => !lesson.completed
              );
              if (firstUncompletedLesson && seriesItem.defaultCourse.lessons) {
                seriesItem.todayLesson = seriesItem.defaultCourse.lessons.find(
                  (lesson) =>
                    lesson.lessonName === firstUncompletedLesson.lessonName
                );
              }
            }
          }
        } catch (error) {
          console.warn(`获取系列 ${category.id} 的练习数据失败:`, error);
        }

        console.log(`系列 ${category.id} 最终数据:`, seriesItem);
        return seriesItem;
      });

      const resolvedSeries = await Promise.all(seriesPromises);
      console.log("所有系列数据解析完成:", resolvedSeries);
      courseSeriesList.value = resolvedSeries;
    } else {
      console.log("分类数据为空或不是数组，设置空数组");
      courseSeriesList.value = [];
    }
  } catch (error) {
    console.error("获取课程系列列表失败:", error);
    uni.showToast({
      title: "获取数据失败",
      icon: "none",
    });
    courseSeriesList.value = [];
  } finally {
    console.log("设置loading为false，最终数据:", courseSeriesList.value);
    loading.value = false;
  }
};

// 格式化进度
const formatProgress = (item: CourseSeriesItem): string => {
  if (item.progressStats) {
    return `已学${item.progressStats.completedLessons}节/共${item.progressStats.totalLessons}节`;
  }

  if (item.defaultCourse?.lessons) {
    const total = item.defaultCourse.lessons.length;
    const completed = item.defaultCourse.lessons.filter(
      (lesson) => lesson.autoUnlock === 0
    ).length;
    return `已学${completed}节/共${total}节`;
  }

  return "已学0节/共0节";
};

// 处理课程系列点击
const handleCourseSeriesClick = (item: CourseSeriesItem) => {
  emit("course-series-click", item);
};

// 组件挂载时获取数据
onMounted(() => {
  console.log("course-series-list 组件已挂载，开始获取数据");
  fetchCourseSeriesList();
});

// 暴露刷新方法
const refresh = () => {
  fetchCourseSeriesList();
};

defineExpose({
  refresh,
});
</script>

<style lang="scss" scoped>
.course-series-list {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;

  text {
    font-size: 16px;
    color: #666;
  }
}

.series-content {
  padding: 20px;
}

.course-series-item {
  margin-bottom: 20px;
}

.course-info-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .course-cover {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    margin-right: 15px;
  }

  .course-info {
    flex: 1;

    .course-name {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      display: block;
      margin-bottom: 5px;
    }

    .course-progress {
      font-size: 14px;
      color: #666;
      display: block;
      margin-bottom: 5px;
    }

    .course-count {
      font-size: 12px;
      color: #999;
      background-color: #f5f5f5;
      padding: 2px 8px;
      border-radius: 10px;
      display: inline-block;
    }
  }

  .action-arrow {
    .arrow-icon {
      font-size: 24px;
      color: #ccc;
    }
  }

  &:active {
    transform: scale(0.98);
    opacity: 0.8;
  }
}

.today-lesson-card {
  background: linear-gradient(135deg, #ffeaa7, #fab1a0);
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;

  .today-label {
    font-size: 14px;
    font-weight: bold;
    color: #2d3436;
    margin-bottom: 10px;
  }

  .lesson-item {
    display: flex;
    align-items: center;

    .lesson-icon {
      width: 50px;
      height: 50px;
      border-radius: 6px;
      margin-right: 12px;
    }

    .lesson-info {
      flex: 1;

      .lesson-name {
        font-size: 16px;
        font-weight: bold;
        color: #2d3436;
        display: block;
        margin-bottom: 4px;
      }

      .lesson-desc {
        font-size: 14px;
        color: #636e72;
        display: block;
      }
    }

    .lesson-status {
      .status-icon {
        font-size: 18px;
      }
    }
  }
}
</style>

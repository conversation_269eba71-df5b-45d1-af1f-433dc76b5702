# 课程系列详情接口重构说明

## 修改概述

根据用户需求，对 `/series/{seriesId}/detail` 接口进行了重构，主要包括：

1. 修改接口参数接收方式，从路径参数改为查询参数
2. 简化返回数据结构，去掉 defaultCourse 和 allCourses 的区分
3. 修改前端代码以适配新的数据结构

## 后端修改

### 1. 接口参数修改

**文件**: `AppWbClassCourseController.java`

```java
// 修改前
@GetMapping("/series/{seriesId}/detail")
public CommonResult<AppWbClassCourseSeriesDetailRespVO> getSeriesDetail(@PathVariable("seriesId") Long seriesId)

// 修改后  
@GetMapping("/series/detail")
public CommonResult<AppWbClassCourseSeriesDetailRespVO> getSeriesDetail(@RequestParam("seriesId") Long seriesId)
```

### 2. 响应数据结构修改

**文件**: `AppWbClassCourseSeriesDetailRespVO.java`

```java
// 修改前
@ApiModelProperty(value = "默认课程", required = true)
private CourseInfo defaultCourse;

@ApiModelProperty(value = "系列内所有课程", required = true)
private List<CourseInfo> allCourses;

// 修改后
@ApiModelProperty(value = "系列内课程列表", required = true)
private List<CourseInfo> courses;
```

### 3. 转换器逻辑修改

**文件**: `WbClassCourseSeriesConvert.java`

```java
// 修改前
result.setAllCourses(courseInfos);
// 设置默认课程（第一个课程）
if (!courseInfos.isEmpty()) {
    result.setDefaultCourse(courseInfos.get(0));
}

// 修改后
result.setCourses(courseInfos);
```

### 4. 控制器逻辑修改

**文件**: `AppWbClassCourseController.java`

```java
// 修改前
// 填充默认课程的lesson详情
if (result != null && result.getDefaultCourse() != null) {
    fillCourseLessons(result.getDefaultCourse());
}
// 填充所有课程的lesson详情
if (result != null && result.getAllCourses() != null) {
    for (AppWbClassCourseSeriesDetailRespVO.CourseInfo courseInfo : result.getAllCourses()) {
        fillCourseLessons(courseInfo);
    }
}

// 修改后
// 填充所有课程的lesson详情
if (result != null && result.getCourses() != null) {
    for (AppWbClassCourseSeriesDetailRespVO.CourseInfo courseInfo : result.getCourses()) {
        fillCourseLessons(courseInfo);
    }
}
```

## 前端修改

### 1. API 调用修改

**文件**: `api/homework.ts`

```typescript
// 修改前
export function getCourseSeriesDetail(seriesId: number) {
  return http.get<CourseSeriesDetail>(
    `/wbclass/course/series/${seriesId}/detail`
  );
}

// 修改后
export function getCourseSeriesDetail(seriesId: number) {
  return http.get<CourseSeriesDetail>(
    `/wbclass/course/series/detail?seriesId=${seriesId}`
  );
}
```

### 2. 数据接口定义修改

**文件**: `api/homework.ts`

```typescript
// 修改前
export interface CourseSeriesDetail {
  seriesId: number;
  seriesName: string;
  defaultCourse: CourseInfo;
  allCourses: CourseInfo[];
}

// 修改后
export interface CourseSeriesDetail {
  seriesId: number;
  seriesName: string;
  courses: CourseInfo[];
}
```

### 3. 页面组件修改

**文件**: `pages/course-series-detail/course-series-detail.vue`

```vue
<!-- 修改前 -->
<picker
  :value="state.currentCourseIndex"
  :range="state.seriesDetail.allCourses"
  range-key="volumeName"
  @change="onCourseChange"
>

<!-- 修改后 -->
<picker
  :value="state.currentCourseIndex"
  :range="state.seriesDetail.courses"
  range-key="volumeName"
  @change="onCourseChange"
>
```

```javascript
// 修改前
state.currentCourse = res.data.defaultCourse;
if (state.seriesDetail && state.seriesDetail.allCourses[index]) {
  state.currentCourse = state.seriesDetail.allCourses[index];
}

// 修改后
state.currentCourse = res.data.courses && res.data.courses.length > 0 ? res.data.courses[0] : null;
if (state.seriesDetail && state.seriesDetail.courses[index]) {
  state.currentCourse = state.seriesDetail.courses[index];
}
```

**文件**: `pages/study/course-series-list.vue`

```vue
<!-- 修改前 -->
<text class="series-desc">共{{ seriesDetail.allCourses?.length || 0 }}册课程</text>
<view v-if="seriesDetail?.allCourses" class="course-list">
  <view v-for="course in seriesDetail.allCourses" :key="course.id">

<!-- 修改后 -->
<text class="series-desc">共{{ seriesDetail.courses?.length || 0 }}册课程</text>
<view v-if="seriesDetail?.courses" class="course-list">
  <view v-for="course in seriesDetail.courses" :key="course.id">
```

## 修改效果

1. **接口调用方式**: 从 `/series/1/detail` 改为 `/series/detail?seriesId=1`
2. **数据结构简化**: 去掉了 defaultCourse 和 allCourses 的区分，统一使用 courses 数组
3. **默认课程逻辑**: 前端将 courses 数组的第一个元素作为默认课程
4. **向后兼容**: 保持了所有课程的 lesson 数据填充功能

## 测试建议

1. 测试接口调用是否正常返回数据
2. 验证前端页面是否正确显示课程列表
3. 确认课程切换功能是否正常工作
4. 检查 lesson 数据是否正确加载和显示

## 注意事项

- 此次修改保持了原有的功能逻辑，只是简化了数据结构
- 所有课程的 lesson 数据仍然会被完整填充
- 前端页面的显示效果应该与之前保持一致
- 如果有其他地方调用了这个接口，需要相应更新调用方式

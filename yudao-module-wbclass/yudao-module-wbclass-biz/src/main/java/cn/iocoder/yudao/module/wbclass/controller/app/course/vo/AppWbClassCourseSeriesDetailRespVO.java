package cn.iocoder.yudao.module.wbclass.controller.app.course.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 课程系列详情响应 VO
 * 用于返回特定系列的详细信息和课程列表
 *
 * <AUTHOR>
 */
@ApiModel("课程系列详情响应 VO")
@Data
public class AppWbClassCourseSeriesDetailRespVO {

    @ApiModelProperty(value = "系列ID", required = true, example = "1")
    private Long seriesId;

    @ApiModelProperty(value = "系列名称", required = true, example = "RE 阅读营")
    private String seriesName;

    @ApiModelProperty(value = "系列内课程列表", required = true)
    private List<CourseInfo> courses;

    @ApiModel("课程信息")
    @Data
    public static class CourseInfo {
        @ApiModelProperty(value = "课程ID", required = true, example = "1")
        private Long id;

        @ApiModelProperty(value = "课程名称", required = true, example = "RE 阅读营 1-3册")
        private String name;

        @ApiModelProperty(value = "册名称", example = "1-3册")
        private String volumeName;

        @ApiModelProperty(value = "课程封面", example = "https://example.com/cover.jpg")
        private String coverUrl;

        @ApiModelProperty(value = "课程描述", example = "基础阅读理解训练")
        private String description;

        @ApiModelProperty(value = "简短描述", example = "基础阅读理解训练")
        private String shortDescription;

        @ApiModelProperty(value = "系列内排序", example = "1")
        private Integer seriesOrder;

        @ApiModelProperty(value = "课程状态", example = "1")
        private Integer status;

        @ApiModelProperty(value = "课节列表", required = true)
        private List<LessonInfo> lessons;
    }

    @ApiModel("课节信息")
    @Data
    public static class LessonInfo {
        @ApiModelProperty(value = "课节ID", required = true, example = "1")
        private Long id;

        @ApiModelProperty(value = "课节名称", required = true, example = "Lesson 1")
        private String lessonName;

        @ApiModelProperty(value = "课节描述", example = "Unit1 A篇-The Amalfi Coast")
        private String lessonDescription;

        @ApiModelProperty(value = "课节封面", example = "https://example.com/lesson-cover.jpg")
        private String lessonCoverUrl;

        @ApiModelProperty(value = "排序号", example = "1")
        private Integer sort;

        @ApiModelProperty(value = "练习数量", example = "5")
        private Integer exerciseCount;

        @ApiModelProperty(value = "解锁类型：1-顺序解锁，2-指定时间解锁，3-完成条件解锁", example = "1")
        private Integer unlockType;

        @ApiModelProperty(value = "是否上锁：0-不上锁，1-上锁", example = "0")
        private Integer autoUnlock;
    }

}
